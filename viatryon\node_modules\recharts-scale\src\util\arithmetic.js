/**
 * @fileOverview 一些公用的运算方法
 * <AUTHOR>
 * @date 2015-09-17
 */
import Decimal from 'decimal.js-light';
import { curry } from './utils';

/**
 * 获取数值的位数
 * 其中绝对值属于区间[0.1, 1)， 得到的值为0
 * 绝对值属于区间[0.01, 0.1)，得到的位数为 -1
 * 绝对值属于区间[0.001, 0.01)，得到的位数为 -2
 *
 * @param  {Number} value 数值
 * @return {Integer} 位数
 */
function getDigitCount(value) {
  let result;

  if (value === 0) {
    result = 1;
  } else {
    result = Math.floor(new Decimal(value).abs().log(10)
      .toNumber()) + 1;
  }

  return result;
}

/**
 * 按照固定的步长获取[start, end)这个区间的数据
 * 并且需要处理js计算精度的问题
 *
 * @param  {Decimal} start 起点
 * @param  {Decimal} end   终点，不包含该值
 * @param  {Decimal} step  步长
 * @return {Array}         若干数值
 */
function rangeStep(start, end, step) {
  let num = new Decimal(start);
  let i = 0;
  const result = [];

  // magic number to prevent infinite loop
  while (num.lt(end) && i < 100000) {
    result.push(num.toNumber());

    num = num.add(step);
    i++;
  }

  return result;
}
/**
 * 对数值进行线性插值
 *
 * @param  {Number} a  定义域的极点
 * @param  {Number} b  定义域的极点
 * @param  {Number} t  [0, 1]内的某个值
 * @return {Number}    定义域内的某个值
 */
const interpolateNumber = curry((a, b, t) => {
  const newA = +a;
  const newB = +b;

  return newA + t * (newB - newA);
});
/**
 * 线性插值的逆运算
 *
 * @param  {Number} a 定义域的极点
 * @param  {Number} b 定义域的极点
 * @param  {Number} x 可以认为是插值后的一个输出值
 * @return {Number}   当x在 a ~ b这个范围内时，返回值属于[0, 1]
 */
const uninterpolateNumber = curry((a, b, x) => {
  let diff = b - (+a);

  diff = diff || Infinity;

  return (x - a) / diff;
});
/**
 * 线性插值的逆运算，并且有截断的操作
 *
 * @param  {Number} a 定义域的极点
 * @param  {Number} b 定义域的极点
 * @param  {Number} x 可以认为是插值后的一个输出值
 * @return {Number}   当x在 a ~ b这个区间内时，返回值属于[0, 1]，
 * 当x不在 a ~ b这个区间时，会截断到 a ~ b 这个区间
 */
const uninterpolateTruncation = curry((a, b, x) => {
  let diff = b - (+a);

  diff = diff || Infinity;

  return Math.max(0, Math.min(1, (x - a) / diff));
});

export default {
  rangeStep,
  getDigitCount,

  interpolateNumber,
  uninterpolateNumber,
  uninterpolateTruncation,
};
