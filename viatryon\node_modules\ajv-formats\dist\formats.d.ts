import type { Format } from "ajv";
export declare type FormatMode = "fast" | "full";
export declare type FormatName = "date" | "time" | "date-time" | "duration" | "uri" | "uri-reference" | "uri-template" | "url" | "email" | "hostname" | "ipv4" | "ipv6" | "regex" | "uuid" | "json-pointer" | "json-pointer-uri-fragment" | "relative-json-pointer" | "byte" | "int32" | "int64" | "float" | "double" | "password" | "binary";
export declare type DefinedFormats = {
    [key in FormatName]: Format;
};
export declare const fullFormats: DefinedFormats;
export declare const fastFormats: DefinedFormats;
export declare const formatNames: FormatName[];
